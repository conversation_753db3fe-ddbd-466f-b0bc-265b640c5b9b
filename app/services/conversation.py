from pydantic import BaseModel

from app.store.messages import MessageStore


class ConversationService(BaseModel):
    async def get_conversation_by_id(self, user_id: str, conversation_id: str):
        # Get the specific conversation
        message_store = MessageStore()
        conversation = await message_store.get_conversation_by_id(user_id, conversation_id)

        return conversation

    async def get_conversations_for_user(self, user_id: str):
        message_store = MessageStore()
        conversations = await message_store.get_conversations_for_user(user_id)
        return conversations