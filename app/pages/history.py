from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi import Request, APIRouter

from app.pages import templates
from app.services.conversation import ConversationService
from app.store.messages import MessageStore
from app.auth import get_current_user

router = APIRouter()

@router.get("/history", response_class=HTMLResponse)
async def conversation_history(request: Request):
    # Require authentication
    user = get_current_user(request)
    if not user:
        return RedirectResponse("/login", status_code=302)

    # Get conversation history for the user
    service = ConversationService()
    conversations = await service.get_conversations_for_user(user.get("sub"))

    # Render the history page
    return templates.TemplateResponse("history.html", {
        "request": request, 
        "user": user,
        "conversations": conversations
    })
