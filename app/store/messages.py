from app.store.db import DB

class MessageStore:
    async def save_message(self, user_id, message_id, parent_id, prompt, response: str):
        db = DB()
        try:
            await db.connect()
            await db.conn.execute('''
INSERT INTO public.messages
(user_id, provider_message_id, parent_message_id, prompt, response)
VALUES($1, $2, $3, $4, $5);''', user_id, message_id, parent_id, prompt, response)
            await db.disconnect()
        except Exception as e:
            print(f"Error saving message: {e}")
        finally:
            await db.disconnect()

    async def get_conversations_for_user(self, user_id: str):
        """
        Get conversation starters (first messages) for a user.
        Each conversation is identified by its first message (parent_message_id is NULL).
        """
        db = DB()
        try:
            await db.connect()

            # Get only the first messages of each conversation (where parent_message_id is NULL)
            rows = await db.conn.fetch('''
                SELECT
                    id,
                    provider_message_id,
                    parent_message_id,
                    prompt,
                    response,
                    created_date,
                    user_id
                FROM public.messages
                WHERE user_id = $1 AND parent_message_id IS NULL
                ORDER BY created_date DESC
            ''', user_id)

            if not rows:
                return []

            conversations = []
            for row in rows:
                conversations.append({
                    'conversation_id': row['provider_message_id'],
                    'started_at': row['created_date'],
                    'messages': [{
                        'id': str(row['id']),
                        'provider_message_id': row['provider_message_id'],
                        'parent_message_id': row['parent_message_id'],
                        'prompt': row['prompt'],
                        'response': row['response'],
                        'created_date': row['created_date'],
                        'user_id': row['user_id'],
                    }]
                })

            return conversations

        except Exception as e:
            print(f"Error fetching conversations: {e}")
            return []
        finally:
            await db.disconnect()

    async def get_conversation_by_id(self, user_id: str, conversation_id: str):
        """
        Get a specific conversation by its ID for a user using recursive SQL.
        The conversation_id should be the root message (parent_message_id IS NULL).
        """
        db = DB()
        try:
            await db.connect()

            # Get all messages in the conversation thread using recursive CTE
            rows = await db.conn.fetch('''
                WITH RECURSIVE conversation AS (
                    SELECT id, provider_message_id, parent_message_id, prompt, response, created_date, user_id
                    FROM public.messages
                    WHERE user_id = $1 AND provider_message_id = $2
                    UNION ALL
                    SELECT m.id, m.provider_message_id, m.parent_message_id, m.prompt, m.response, m.created_date, m.user_id
                    FROM public.messages m
                    JOIN conversation c ON m.parent_message_id = c.provider_message_id
                    WHERE m.user_id = $1
                )
                SELECT * FROM conversation ORDER BY created_date ASC
            ''', user_id, conversation_id)

            if not rows:
                return None

            # Convert rows to message objects
            messages = []
            for row in rows:
                messages.append({
                    'id': str(row['id']),
                    'provider_message_id': row['provider_message_id'],
                    'parent_message_id': row['parent_message_id'],
                    'prompt': row['prompt'],
                    'response': row['response'],
                    'created_date': row['created_date'],
                    'user_id': row['user_id'],
                })

            # Get the latest response ID (last message in the conversation)
            latest_response_id = messages[-1]['provider_message_id'] if messages else None

            return {
                'conversation_id': conversation_id,
                'messages': messages,
                'latest_response_id': latest_response_id
            }

        except Exception as e:
            print(f"Error fetching conversation: {e}")
            return None
        finally:
            await db.disconnect()



