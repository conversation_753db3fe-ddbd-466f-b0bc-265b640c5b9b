import asyncpg

from app.core.config import settings


class DB:
    def __init__(self):
        self.conn = None

    async def connect(self) -> None:
        try:
            self.conn = await asyncpg.connect(
                host=settings.DB_HOST,
                port=settings.DB_PORT,
                database=settings.DB_NAME,
                user=settings.DB_USER,
                password=settings.DB_PASSWORD
            )
            print("Successfully connected to Gooda database")
        except Exception as e:
            print(f"Error connecting to database: {e}")

    async def disconnect(self) -> None:
        if self.conn:
            await self.conn.close()
            print("Database connection closed")