import os

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    OPENAI_API_KEY: str = os.environ.get('OPENAI_API_KEY', '')
    PROJECT_ID: str = os.environ.get('OPENAI_PROJECT_ID', '')

    # Session/Auth settings
    SECRET_KEY: str = os.environ.get('SECRET_KEY', '')

    # Google OAuth (OIDC) credentials
    GOOGLE_CLIENT_ID: str = os.environ.get('GOOGLE_CLIENT_ID', '')
    GOOGLE_CLIENT_SECRET: str = os.environ.get('GOOGLE_CLIENT_SECRET', '')

    # Authentication middleware settings
    PROTECTED_PATHS: list[str] = ["/api/v1/"]

    DB_USER: str = os.environ.get('DB_USER', '')
    DB_PASSWORD: str = os.environ.get('DB_PASSWORD', '')
    DB_HOST: str = os.environ.get('DB_HOST', 'localhost')
    DB_PORT: str = os.environ.get('DB_PORT', '5432')
    DB_NAME: str = os.environ.get('DB_NAME', 'postgres')


settings = Settings()