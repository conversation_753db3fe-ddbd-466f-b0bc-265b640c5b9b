from fastapi import APIRouter, Request, HTTPException

from app.auth import get_current_user
from app.services.conversation import ConversationService

router = APIRouter()

@router.get("/conversation/{conversation_id}")
async def get_conversation(conversation_id: str, request: Request):
    # Get current user
    user = get_current_user(request)
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    user_id = user.get("sub")

    # Get the specific conversation
    service = ConversationService()
    conversation = await service.get_conversation_by_id(user_id, conversation_id)

    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")

    return conversation
