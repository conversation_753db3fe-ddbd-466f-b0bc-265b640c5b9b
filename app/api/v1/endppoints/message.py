from fastapi import APIRouter, Request

from app.models.prompt_request import PromptRequest
from app.services.message import MessageService
from app.auth import get_current_user

router = APIRouter()

@router.post("/therapist")
async def get_therapist_response(p: PromptRequest, request: Request):
    # The middleware already validates authentication, but we can get user info if needed
    user = get_current_user(request)

    # Extract user ID from the authenticated user session
    user_id = user.get("sub") if user else "anonymous"

    service = MessageService()
    resp = await service.get_therapist_response(p, user_id)
    return resp
