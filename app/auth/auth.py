from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)


def get_current_user(request: Request) -> dict | None:
    """
    Utility function to get the current authenticated user from the session.

    Args:
        request: The FastAPI request object

    Returns:
        dict: User data if authenticated, None otherwise
    """
    try:
        user = request.session.get("user")
        if not user:
            return None

        # Validate that the user session has required fields
        required_fields = ["sub", "email"]
        if not all(field in user for field in required_fields):
            return None

        # Ensure the user ID (sub) is not empty
        if not user.get("sub"):
            return None

        return user

    except Exception as e:
        logger.error(f"Error getting current user: {e}")
        return None


def require_auth(request: Request) -> dict:
    """
    Utility function to require authentication and return the user data.
    Raises HTTPException if not authenticated.

    Args:
        request: The FastAPI request object

    Returns:
        dict: User data

    Raises:
        HTTPException: If user is not authenticated
    """
    user = get_current_user(request)
    if not user:
        raise HTTPException(
            status_code=401,
            detail="Authentication required. Please log in to access this resource."
        )
    return user


class AuthenticationMiddleware(BaseHTTPMiddleware):
    """
    Middleware to protect API endpoints by validating session-based authentication.
    
    This middleware checks for authenticated user sessions on API routes.
    If no valid session is found, it returns a 401 Unauthorized response.
    """
    
    def __init__(self, app, protected_paths: list[str] = None):
        super().__init__(app)
        # Use protected paths from config or allow override
        self.protected_paths = protected_paths or settings.PROTECTED_PATHS
    
    async def dispatch(self, request: Request, call_next):
        # Check if the request path should be protected
        if self._should_protect_path(request.url.path):
            # Validate authentication
            if not self._is_authenticated(request):
                logger.warning(f"Unauthorized API access attempt to {request.url.path}")
                return JSONResponse(
                    status_code=401,
                    content={
                        "detail": "Authentication required. Please log in to access this resource.",
                        "error": "unauthorized"
                    }
                )
        
        # Continue with the request if authenticated or not protected
        response = await call_next(request)
        return response
    
    def _should_protect_path(self, path: str) -> bool:
        """
        Determine if a given path should be protected by authentication.
        
        Args:
            path: The request path to check
            
        Returns:
            bool: True if the path should be protected, False otherwise
        """
        return any(path.startswith(protected_path) for protected_path in self.protected_paths)
    
    def _is_authenticated(self, request: Request) -> bool:
        """
        Check if the request has a valid authenticated session.
        
        Args:
            request: The FastAPI request object
            
        Returns:
            bool: True if the user is authenticated, False otherwise
        """
        try:
            # Check if session exists and has user data
            user = request.session.get("user")
            if not user:
                return False
            
            # Validate that the user session has required fields
            required_fields = ["sub", "email"]
            if not all(field in user for field in required_fields):
                logger.warning(f"Invalid user session data: missing required fields")
                return False
            
            # Additional validation: ensure the user ID (sub) is not empty
            if not user.get("sub"):
                logger.warning(f"Invalid user session: empty user ID")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Error validating authentication: {e}")
            return False
