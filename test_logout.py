#!/usr/bin/env python3
"""
Test script to verify logout functionality works correctly.
"""

import asyncio
import aiohttp
from urllib.parse import urlparse, parse_qs


async def test_logout_endpoints():
    """Test both logout endpoints to ensure they work correctly."""
    print("Testing logout functionality...")
    
    async with aiohttp.ClientSession() as session:
        # Test 1: Regular logout endpoint
        print("\n1. Testing /logout endpoint...")
        try:
            async with session.get('http://localhost:8001/logout', allow_redirects=False) as response:
                print(f"Status: {response.status}")
                print(f"Location header: {response.headers.get('location', 'None')}")
                
                # Check if it redirects to login
                if response.status == 307 and response.headers.get('location') == '/login':
                    print("✅ SUCCESS: /logout redirects to /login correctly")
                else:
                    print("❌ FAILURE: /logout doesn't redirect properly")
                    
                # Check if session cookie is being cleared
                set_cookie = response.headers.get('set-cookie', '')
                if 'session=' in set_cookie and ('Max-Age=0' in set_cookie or 'expires=' in set_cookie):
                    print("✅ SUCCESS: Session cookie is being cleared")
                else:
                    print("⚠️  WARNING: Session cookie clearing not detected in headers")
                    
        except Exception as e:
            print(f"❌ ERROR testing /logout: {e}")

        # Test 2: Login page accessibility
        print("\n2. Testing /login endpoint...")
        try:
            async with session.get('http://localhost:8001/login') as response:
                print(f"Status: {response.status}")
                if response.status == 200:
                    content = await response.text()
                    if 'Sign in with Google' in content:
                        print("✅ SUCCESS: Login page loads correctly")
                    else:
                        print("⚠️  WARNING: Login page content may be incorrect")
                else:
                    print("❌ FAILURE: Login page not accessible")
                    
        except Exception as e:
            print(f"❌ ERROR testing /login: {e}")

        # Test 3: Auth Google endpoint
        print("\n3. Testing /auth/google endpoint...")
        try:
            async with session.get('http://localhost:8001/auth/google', allow_redirects=False) as response:
                print(f"Status: {response.status}")
                location = response.headers.get('location', '')
                
                if response.status == 307 and 'accounts.google.com' in location:
                    print("✅ SUCCESS: /auth/google redirects to Google OAuth")
                    
                    # Check if prompt=select_account is included
                    if 'prompt=select_account' in location:
                        print("✅ SUCCESS: prompt=select_account parameter is included")
                    else:
                        print("⚠️  WARNING: prompt=select_account parameter not found")
                else:
                    print("❌ FAILURE: /auth/google doesn't redirect to Google OAuth")
                    
        except Exception as e:
            print(f"❌ ERROR testing /auth/google: {e}")


async def main():
    print("Logout Functionality Test")
    print("=" * 50)
    print("Make sure the server is running on http://localhost:8001")
    print("You can start it with: python3 -m uvicorn app.main:app --reload --port 8001")
    print()
    
    try:
        await test_logout_endpoints()
    except Exception as e:
        print(f"Test failed with error: {e}")
    
    print("\n" + "=" * 50)
    print("Test completed!")


if __name__ == "__main__":
    asyncio.run(main())
